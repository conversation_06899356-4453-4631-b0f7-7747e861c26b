CREATE TABLE `wdt_saledetail` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `订单编号` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单编号',
  `原始单号` text COLLATE utf8mb4_unicode_ci COMMENT '原始单号',
  `子单原始单号` text COLLATE utf8mb4_unicode_ci COMMENT '子单原始单号',
  `原始子订单号` text COLLATE utf8mb4_unicode_ci COMMENT '原始子订单号',
  `订单类型` text COLLATE utf8mb4_unicode_ci COMMENT '订单类型',
  `支付账号` text COLLATE utf8mb4_unicode_ci COMMENT '支付账号',
  `出库单编号` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '出库单编号',
  `仓库` text COLLATE utf8mb4_unicode_ci COMMENT '仓库',
  `仓库类型` text COLLATE utf8mb4_unicode_ci COMMENT '仓库类型',
  `店铺` text COLLATE utf8mb4_unicode_ci COMMENT '店铺',
  `出库单状态` text COLLATE utf8mb4_unicode_ci COMMENT '出库单状态',
  `出库状态` text COLLATE utf8mb4_unicode_ci COMMENT '出库状态',
  `分拣序号` text COLLATE utf8mb4_unicode_ci COMMENT '分拣序号',
  `商家编码` text COLLATE utf8mb4_unicode_ci COMMENT '商家编码',
  `货品编号` text COLLATE utf8mb4_unicode_ci COMMENT '货品编号',
  `货品名称` text COLLATE utf8mb4_unicode_ci COMMENT '货品名称',
  `货品简称` text COLLATE utf8mb4_unicode_ci COMMENT '货品简称',
  `品牌` text COLLATE utf8mb4_unicode_ci COMMENT '品牌',
  `分类` text COLLATE utf8mb4_unicode_ci COMMENT '分类',
  `规格码` text COLLATE utf8mb4_unicode_ci COMMENT '规格码',
  `规格名称` text COLLATE utf8mb4_unicode_ci COMMENT '规格名称',
  `平台货品名称` text COLLATE utf8mb4_unicode_ci COMMENT '平台货品名称',
  `平台规格名称` text COLLATE utf8mb4_unicode_ci COMMENT '平台规格名称',
  `平台货品ID` text COLLATE utf8mb4_unicode_ci COMMENT '平台货品ID',
  `平台规格ID` text COLLATE utf8mb4_unicode_ci COMMENT '平台规格ID',
  `条形码` text COLLATE utf8mb4_unicode_ci COMMENT '条形码',
  `货品数量` int NOT NULL COMMENT '货品数量',
  `货品原单价` decimal(10,2) DEFAULT NULL COMMENT '货品原单价',
  `货品原总金额` decimal(10,2) DEFAULT NULL COMMENT '货品原总金额',
  `订单总优惠` decimal(10,2) DEFAULT NULL COMMENT '订单总优惠',
  `邮费` decimal(10,2) DEFAULT NULL COMMENT '邮费',
  `货品成交价` decimal(10,2) DEFAULT NULL COMMENT '货品成交价',
  `货品成交总价` decimal(10,2) DEFAULT NULL COMMENT '货品成交总价',
  `货品总优惠` decimal(10,2) DEFAULT NULL COMMENT '货品总优惠',
  `货到付款金额` decimal(10,2) DEFAULT NULL COMMENT '货到付款金额',
  `货品成本` decimal(10,2) DEFAULT NULL COMMENT '货品成本',
  `货品总成本` decimal(10,2) DEFAULT NULL COMMENT '货品总成本',
  `固定成本` decimal(10,2) DEFAULT NULL COMMENT '固定成本',
  `固定总成本` decimal(10,2) DEFAULT NULL COMMENT '固定总成本',
  `订单支付金额` decimal(10,2) DEFAULT NULL COMMENT '订单支付金额',
  `应收金额` decimal(10,2) DEFAULT NULL COMMENT '应收金额',
  `退款前支付金额` decimal(10,2) DEFAULT NULL COMMENT '退款前支付金额',
  `单品支付金额` decimal(10,2) DEFAULT NULL COMMENT '单品支付金额',
  `分摊邮费` decimal(10,2) DEFAULT NULL COMMENT '分摊邮费',
  `预估邮资` decimal(10,2) DEFAULT NULL COMMENT '预估邮资',
  `邮资成本` decimal(10,2) DEFAULT NULL COMMENT '邮资成本',
  `订单包装成本` decimal(10,2) DEFAULT NULL COMMENT '订单包装成本',
  `订单毛利` decimal(10,2) DEFAULT NULL COMMENT '订单毛利',
  `毛利率` decimal(10,2) DEFAULT NULL COMMENT '毛利率',
  `订单固定毛利` decimal(10,2) DEFAULT NULL COMMENT '订单固定毛利',
  `固定毛利率` decimal(10,2) DEFAULT NULL COMMENT '固定毛利率',
  `客户网名` text COLLATE utf8mb4_unicode_ci COMMENT '客户网名',
  `收件人` text COLLATE utf8mb4_unicode_ci COMMENT '收件人',
  `证件号码` text COLLATE utf8mb4_unicode_ci COMMENT '证件号码',
  `收货地区` text COLLATE utf8mb4_unicode_ci COMMENT '收货地区',
  `收货地址` text COLLATE utf8mb4_unicode_ci COMMENT '收货地址',
  `收件人手机` text COLLATE utf8mb4_unicode_ci COMMENT '收件人手机',
  `收件人电话` text COLLATE utf8mb4_unicode_ci COMMENT '收件人电话',
  `物流公司` text COLLATE utf8mb4_unicode_ci COMMENT '物流公司',
  `实际重量` decimal(10,2) DEFAULT NULL COMMENT '实际重量',
  `预估重量` decimal(10,2) DEFAULT NULL COMMENT '预估重量',
  `需开发票` text COLLATE utf8mb4_unicode_ci COMMENT '需开发票',
  `制单人` text COLLATE utf8mb4_unicode_ci COMMENT '制单人',
  `打单员` text COLLATE utf8mb4_unicode_ci COMMENT '打单员',
  `拣货员` text COLLATE utf8mb4_unicode_ci COMMENT '拣货员',
  `打包员` text COLLATE utf8mb4_unicode_ci COMMENT '打包员',
  `检视员` text COLLATE utf8mb4_unicode_ci COMMENT '检视员',
  `业务员` text COLLATE utf8mb4_unicode_ci COMMENT '业务员',
  `验货员` text COLLATE utf8mb4_unicode_ci COMMENT '验货员',
  `打印波次` text COLLATE utf8mb4_unicode_ci COMMENT '打印波次',
  `物流单打印状态` text COLLATE utf8mb4_unicode_ci COMMENT '物流单打印状态',
  `发货单打印状态` text COLLATE utf8mb4_unicode_ci COMMENT '发货单打印状态',
  `分拣单打印状态` text COLLATE utf8mb4_unicode_ci COMMENT '分拣单打印状态',
  `物流单号` text COLLATE utf8mb4_unicode_ci COMMENT '物流单号',
  `分拣单编号` text COLLATE utf8mb4_unicode_ci COMMENT '分拣单编号',
  `外部单号` text COLLATE utf8mb4_unicode_ci COMMENT '外部单号',
  `付款时间` datetime DEFAULT NULL COMMENT '付款时间',
  `发货时间` datetime DEFAULT NULL COMMENT '发货时间',
  `下单时间` datetime DEFAULT NULL COMMENT '下单时间',
  `审核时间` datetime DEFAULT NULL COMMENT '审核时间',
  `赠品方式` text COLLATE utf8mb4_unicode_ci COMMENT '赠品方式',
  `买家留言` text COLLATE utf8mb4_unicode_ci COMMENT '买家留言',
  `客服备注` text COLLATE utf8mb4_unicode_ci COMMENT '客服备注',
  `打印备注` text COLLATE utf8mb4_unicode_ci COMMENT '打印备注',
  `备注` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `包装` text COLLATE utf8mb4_unicode_ci COMMENT '包装',
  `来源组合装编码` text COLLATE utf8mb4_unicode_ci COMMENT '来源组合装编码',
  `拆自组合装` text COLLATE utf8mb4_unicode_ci COMMENT '拆自组合装',
  `来源组合装数量` int DEFAULT NULL COMMENT '来源组合装数量',
  `体积` decimal(10,2) DEFAULT NULL COMMENT '体积',
  `分销商` text COLLATE utf8mb4_unicode_ci COMMENT '分销商',
  `分销商编号` text COLLATE utf8mb4_unicode_ci COMMENT '分销商编号',
  `分销原始单号` text COLLATE utf8mb4_unicode_ci COMMENT '分销原始单号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='销售出库明细表'