#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的导入测试
"""

import pandas as pd
import pymysql
from pymysql.cursors import DictCursor

# 数据库连接配置
DB_CONFIG = {
    'host': '************',
    'user': 'lxt',
    'password': 'Lxt0307+',
    'database': 'qq_day_sale',
    'charset': 'utf8mb4',
    'autocommit': False
}

def simple_test_import():
    """简化的导入测试"""
    try:
        # 1. 读取Excel文件
        print("1. 读取Excel文件...")
        df = pd.read_excel('sample_data.xlsx')
        print(f"   读取成功，包含 {len(df)} 行数据")
        
        # 2. 数据预处理
        print("2. 数据预处理...")
        df_filled = df.fillna('')
        records = df_filled.to_dict('records')
        print(f"   预处理完成，转换为 {len(records)} 条记录")
        
        # 3. 连接数据库
        print("3. 连接数据库...")
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        print("   数据库连接成功")
        
        # 4. 获取表结构
        print("4. 获取表结构...")
        cursor.execute("DESCRIBE wdt_saledetail")
        columns_info = cursor.fetchall()
        table_fields = [col[0] for col in columns_info]
        print(f"   表包含 {len(table_fields)} 个字段")
        
        # 5. 处理第一条记录
        print("5. 处理第一条记录...")
        if records:
            first_record = records[0]
            print(f"   原始记录字段数: {len(first_record)}")
            
            # 字段映射
            cleaned_record = {}
            for key, value in first_record.items():
                if key in table_fields:
                    # 简单的数据类型转换
                    if value == '':
                        cleaned_record[key] = None
                    else:
                        cleaned_record[key] = value
            
            print(f"   清理后记录字段数: {len(cleaned_record)}")
            print(f"   匹配的字段: {list(cleaned_record.keys())}")
            
            # 6. 尝试插入一条记录
            print("6. 尝试插入记录...")
            if cleaned_record:
                columns = list(cleaned_record.keys())
                placeholders = ', '.join(['%s'] * len(columns))
                sql = f"INSERT INTO wdt_saledetail ({', '.join(columns)}) VALUES ({placeholders})"
                values = [cleaned_record.get(col) for col in columns]
                
                print(f"   SQL: {sql[:100]}...")
                print(f"   值的数量: {len(values)}")
                
                cursor.execute(sql, values)
                connection.commit()
                print("   ✓ 记录插入成功！")
            else:
                print("   ✗ 没有可插入的字段")
        
        connection.close()
        print("\n测试完成！")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    simple_test_import()
