#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建大型示例Excel文件用于测试
"""

import pandas as pd
from datetime import datetime, timedelta
import random

def create_large_sample():
    """创建大型示例Excel文件"""
    print("正在创建大型示例Excel文件...")
    
    # 创建示例数据
    sample_data = []
    
    for i in range(1, 1001):  # 创建1000条示例记录
        # 随机生成一些变化的数据
        quantity = random.randint(1, 10)
        unit_price = round(random.uniform(50.0, 200.0), 2)
        total_price = round(unit_price * quantity, 2)
        
        # 随机时间
        base_time = datetime.now() - timedelta(days=random.randint(1, 30))
        
        record = {
            '订单编号': f'ORDER{i:06d}',
            '原始单号': f'ORIG{i:06d}',
            '出库单编号': f'OUT{i:06d}',
            '仓库': random.choice(['主仓库', '分仓库A', '分仓库B']),
            '店铺': random.choice(['测试店铺', '旗舰店', '专营店']),
            '商家编码': f'GOODS{i:03d}',
            '货品编号': f'ITEM{i:03d}',
            '货品名称': f'测试商品{i}',
            '品牌': random.choice(['品牌A', '品牌B', '品牌C']),
            '分类': random.choice(['电子产品', '服装', '家居用品']),
            '规格名称': f'规格{i}',
            '货品数量': quantity,
            '货品原单价': unit_price,
            '货品原总金额': total_price,
            '货品成交价': round(unit_price * 0.9, 2),
            '货品成交总价': round(total_price * 0.9, 2),
            '客户网名': f'客户{i}',
            '收件人': f'收件人{i}',
            '收货地区': random.choice(['北京市朝阳区', '上海市浦东区', '广州市天河区']),
            '收货地址': f'测试地址{i}号',
            '收件人手机': f'138{i:08d}',
            '物流公司': random.choice(['顺丰快递', '圆通快递', '中通快递']),
            '付款时间': base_time.strftime('%Y-%m-%d %H:%M:%S'),
            '下单时间': (base_time - timedelta(hours=1)).strftime('%Y-%m-%d %H:%M:%S'),
            '备注': f'测试备注{i}'
        }
        sample_data.append(record)
    
    # 创建DataFrame并保存为Excel
    df = pd.DataFrame(sample_data)
    excel_file = 'large_sample_data.xlsx'
    df.to_excel(excel_file, index=False, engine='openpyxl')
    
    print(f"✓ 大型示例Excel文件已创建: {excel_file}")
    print(f"✓ 包含 {len(sample_data)} 条示例记录")
    
    return excel_file

if __name__ == "__main__":
    create_large_sample()
