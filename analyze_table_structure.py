#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库表结构分析脚本
用于分析MySQL数据库中wdt_saledetail表的完整结构
"""

import pymysql
import pandas as pd
from typing import Dict, List, Tuple
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('table_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 数据库连接配置
DB_CONFIG = {
    'host': '************',
    'user': 'lxt',
    'password': 'Lxt0307+',
    'database': 'qq_day_sale',
    'charset': 'utf8mb4',
    'autocommit': False
}

def get_table_structure(table_name: str = 'wdt_saledetail') -> Dict:
    """
    获取表的完整结构信息
    
    Args:
        table_name: 表名
        
    Returns:
        包含表结构信息的字典
    """
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 获取表结构
        cursor.execute(f"DESCRIBE {table_name}")
        columns_info = cursor.fetchall()
        
        # 获取表的创建语句
        cursor.execute(f"SHOW CREATE TABLE {table_name}")
        create_table = cursor.fetchone()[1]
        
        # 获取索引信息
        cursor.execute(f"SHOW INDEX FROM {table_name}")
        indexes_info = cursor.fetchall()
        
        # 获取表统计信息
        cursor.execute(f"""
            SELECT 
                TABLE_ROWS,
                DATA_LENGTH,
                INDEX_LENGTH,
                AUTO_INCREMENT
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = '{DB_CONFIG["database"]}' 
            AND TABLE_NAME = '{table_name}'
        """)
        table_stats = cursor.fetchone()
        
        structure_info = {
            'columns': columns_info,
            'create_statement': create_table,
            'indexes': indexes_info,
            'stats': table_stats
        }
        
        return structure_info
        
    except Exception as e:
        logger.error(f"获取表结构失败: {e}")
        raise
    finally:
        if 'connection' in locals():
            connection.close()

def analyze_columns(columns_info: Tuple) -> List[Dict]:
    """
    分析列信息
    
    Args:
        columns_info: 列信息元组
        
    Returns:
        格式化的列信息列表
    """
    columns = []
    for col in columns_info:
        column_info = {
            'field': col[0],
            'type': col[1],
            'null': col[2],
            'key': col[3],
            'default': col[4],
            'extra': col[5]
        }
        columns.append(column_info)
    
    return columns

def print_table_analysis(table_name: str = 'wdt_saledetail'):
    """
    打印表结构分析结果
    
    Args:
        table_name: 表名
    """
    try:
        logger.info(f"开始分析表 {table_name} 的结构...")
        
        structure = get_table_structure(table_name)
        columns = analyze_columns(structure['columns'])
        
        print(f"\n{'='*80}")
        print(f"表名: {table_name}")
        print(f"{'='*80}")
        
        # 打印列信息
        print(f"\n列信息 (共 {len(columns)} 列):")
        print(f"{'-'*80}")
        print(f"{'字段名':<20} {'数据类型':<20} {'允许NULL':<10} {'键':<10} {'默认值':<15} {'额外信息'}")
        print(f"{'-'*80}")
        
        for col in columns:
            print(f"{col['field']:<20} {col['type']:<20} {col['null']:<10} {col['key']:<10} {str(col['default']):<15} {col['extra']}")
        
        # 打印索引信息
        print(f"\n索引信息:")
        print(f"{'-'*80}")
        if structure['indexes']:
            for idx in structure['indexes']:
                print(f"索引名: {idx[2]}, 列: {idx[4]}, 唯一: {'是' if idx[1] == 0 else '否'}")
        
        # 打印表统计信息
        if structure['stats']:
            print(f"\n表统计信息:")
            print(f"{'-'*40}")
            print(f"行数: {structure['stats'][0] or 0}")
            print(f"数据大小: {(structure['stats'][1] or 0) / 1024 / 1024:.2f} MB")
            print(f"索引大小: {(structure['stats'][2] or 0) / 1024 / 1024:.2f} MB")
            print(f"自增值: {structure['stats'][3] or 'N/A'}")
        
        # 保存列信息到CSV文件
        df_columns = pd.DataFrame(columns)
        df_columns.to_csv(f'{table_name}_columns.csv', index=False, encoding='utf-8-sig')
        logger.info(f"列信息已保存到 {table_name}_columns.csv")
        
        # 保存创建语句
        with open(f'{table_name}_create.sql', 'w', encoding='utf-8') as f:
            f.write(structure['create_statement'])
        logger.info(f"建表语句已保存到 {table_name}_create.sql")
        
        return columns
        
    except Exception as e:
        logger.error(f"分析表结构失败: {e}")
        raise

if __name__ == "__main__":
    try:
        columns = print_table_analysis('wdt_saledetail')
        print(f"\n分析完成！详细信息已保存到相关文件中。")
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
