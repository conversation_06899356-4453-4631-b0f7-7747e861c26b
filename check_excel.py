#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Excel文件内容
"""

import pandas as pd

def check_excel_content():
    """检查Excel文件内容"""
    try:
        # 读取Excel文件
        df = pd.read_excel('sample_data.xlsx')
        
        print("Excel文件信息:")
        print(f"行数: {len(df)}")
        print(f"列数: {len(df.columns)}")
        print(f"列名: {list(df.columns)}")
        
        print("\n前5行数据:")
        print(df.head())
        
        print("\n数据类型:")
        print(df.dtypes)
        
        return df
        
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return None

if __name__ == "__main__":
    check_excel_content()
