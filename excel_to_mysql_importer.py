#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能Excel到MySQL批量导入工具
支持多线程并发导入、大文件处理、断点续传等功能

作者: AI Assistant
版本: 1.0
日期: 2025-08-04
"""

import os
import sys
import time
import logging
import threading
import queue
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
import hashlib
import json

import pandas as pd
import pymysql
from pymysql.cursors import DictCursor
import numpy as np
from tqdm import tqdm

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('excel_import.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ImportConfig:
    """导入配置类"""
    # 数据库连接配置
    db_host: str = '************'
    db_user: str = 'lxt'
    db_password: str = 'Lxt0307+'
    db_database: str = 'qq_day_sale'
    db_charset: str = 'utf8mb4'
    
    # 目标表配置
    target_table: str = 'wdt_saledetail'
    
    # 性能配置
    batch_size: int = 1000  # 每批次插入的记录数
    max_workers: int = 4    # 最大线程数
    chunk_size: int = 10000 # Excel读取块大小
    
    # 功能配置
    enable_resume: bool = True      # 启用断点续传
    enable_validation: bool = True  # 启用数据验证
    skip_duplicates: bool = True    # 跳过重复数据
    
    # 文件配置
    progress_file: str = 'import_progress.json'  # 进度文件
    error_file: str = 'import_errors.xlsx'       # 错误数据文件

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config: ImportConfig):
        self.config = config
        self._local = threading.local()
    
    def get_connection(self):
        """获取线程本地数据库连接"""
        if not hasattr(self._local, 'connection') or self._local.connection is None:
            self._local.connection = pymysql.connect(
                host=self.config.db_host,
                user=self.config.db_user,
                password=self.config.db_password,
                database=self.config.db_database,
                charset=self.config.db_charset,
                autocommit=False,
                cursorclass=DictCursor
            )
        return self._local.connection
    
    def close_connection(self):
        """关闭线程本地连接"""
        if hasattr(self._local, 'connection') and self._local.connection:
            self._local.connection.close()
            self._local.connection = None
    
    def get_table_columns(self, table_name: str) -> List[Dict]:
        """获取表的列信息"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute(f"DESCRIBE {table_name}")
            columns = cursor.fetchall()
            return columns
        except Exception as e:
            logger.error(f"获取表结构失败: {e}")
            raise
        finally:
            cursor.close()
    
    def execute_batch_insert(self, table_name: str, data: List[Dict]) -> Tuple[int, List[Dict]]:
        """
        批量插入数据
        
        Returns:
            (成功插入数量, 失败记录列表)
        """
        if not data:
            return 0, []
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # 构建插入SQL
            columns = list(data[0].keys())
            placeholders = ', '.join(['%s'] * len(columns))
            sql = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
            
            # 准备数据
            values_list = []
            failed_records = []
            
            for record in data:
                try:
                    values = [record.get(col) for col in columns]
                    values_list.append(values)
                except Exception as e:
                    logger.warning(f"准备数据失败: {e}, 记录: {record}")
                    failed_records.append(record)
            
            # 执行批量插入
            if values_list:
                cursor.executemany(sql, values_list)
                conn.commit()
                success_count = len(values_list)
                logger.info(f"成功插入 {success_count} 条记录")
                return success_count, failed_records
            else:
                return 0, failed_records
                
        except Exception as e:
            conn.rollback()
            logger.error(f"批量插入失败: {e}")
            return 0, data
        finally:
            cursor.close()

class DataValidator:
    """数据验证器"""
    
    def __init__(self, table_columns: List[Dict]):
        self.table_columns = table_columns
        self.column_info = {col['Field']: col for col in table_columns}
    
    def validate_record(self, record: Dict) -> Tuple[bool, List[str]]:
        """
        验证单条记录
        
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        for field, value in record.items():
            if field not in self.column_info:
                continue
                
            col_info = self.column_info[field]
            
            # 检查必填字段
            if col_info['Null'] == 'NO' and (value is None or value == ''):
                if col_info['Extra'] != 'auto_increment':  # 跳过自增字段
                    errors.append(f"字段 {field} 不能为空")
            
            # 检查数据类型
            if value is not None and value != '':
                if not self._validate_data_type(field, value, col_info['Type']):
                    errors.append(f"字段 {field} 数据类型不匹配: {value}")
        
        return len(errors) == 0, errors
    
    def _validate_data_type(self, field: str, value: Any, col_type: str) -> bool:
        """验证数据类型"""
        try:
            if 'int' in col_type.lower():
                int(value)
            elif 'decimal' in col_type.lower() or 'float' in col_type.lower():
                float(value)
            elif 'datetime' in col_type.lower():
                if isinstance(value, str):
                    pd.to_datetime(value)
            return True
        except:
            return False

class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, config: ImportConfig):
        self.config = config
        self.progress_file = config.progress_file
        self.lock = threading.Lock()
        self.progress_data = self._load_progress()
    
    def _load_progress(self) -> Dict:
        """加载进度数据"""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return {}
    
    def save_progress(self, file_path: str, processed_rows: int, total_rows: int):
        """保存进度"""
        with self.lock:
            file_hash = self._get_file_hash(file_path)
            self.progress_data[file_hash] = {
                'file_path': file_path,
                'processed_rows': processed_rows,
                'total_rows': total_rows,
                'last_update': datetime.now().isoformat()
            }
            
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(self.progress_data, f, ensure_ascii=False, indent=2)
    
    def get_progress(self, file_path: str) -> Tuple[int, int]:
        """获取文件处理进度"""
        file_hash = self._get_file_hash(file_path)
        if file_hash in self.progress_data:
            data = self.progress_data[file_hash]
            return data.get('processed_rows', 0), data.get('total_rows', 0)
        return 0, 0
    
    def _get_file_hash(self, file_path: str) -> str:
        """计算文件哈希值"""
        with open(file_path, 'rb') as f:
            return hashlib.md5(f.read()).hexdigest()

class ExcelToMySQLImporter:
    """Excel到MySQL导入器主类"""
    
    def __init__(self, config: ImportConfig = None):
        self.config = config or ImportConfig()
        self.db_manager = DatabaseManager(self.config)
        self.progress_tracker = ProgressTracker(self.config)
        self.table_columns = None
        self.validator = None
        
        # 统计信息
        self.stats = {
            'total_processed': 0,
            'total_success': 0,
            'total_failed': 0,
            'start_time': None,
            'end_time': None
        }
    
    def initialize(self):
        """初始化导入器"""
        logger.info("初始化导入器...")
        
        # 获取表结构
        self.table_columns = self.db_manager.get_table_columns(self.config.target_table)
        logger.info(f"目标表 {self.config.target_table} 包含 {len(self.table_columns)} 个字段")
        
        # 初始化验证器
        if self.config.enable_validation:
            self.validator = DataValidator(self.table_columns)
            logger.info("数据验证器已启用")
        
        logger.info("导入器初始化完成")

    def import_excel_file(self, excel_file_path: str, sheet_name: str = None) -> Dict:
        """
        导入Excel文件到MySQL数据库

        Args:
            excel_file_path: Excel文件路径
            sheet_name: 工作表名称，None表示第一个工作表

        Returns:
            导入结果统计
        """
        if not os.path.exists(excel_file_path):
            raise FileNotFoundError(f"Excel文件不存在: {excel_file_path}")

        logger.info(f"开始导入Excel文件: {excel_file_path}")
        self.stats['start_time'] = datetime.now()

        try:
            # 获取断点续传进度
            processed_rows, total_rows = self.progress_tracker.get_progress(excel_file_path)

            if processed_rows > 0 and self.config.enable_resume:
                logger.info(f"检测到断点续传，已处理 {processed_rows}/{total_rows} 行")
                skip_rows = processed_rows
            else:
                skip_rows = 0

            # 读取Excel文件
            self._process_excel_file(
                excel_file_path, sheet_name, skip_rows
            )

            self.stats['end_time'] = datetime.now()
            duration = (self.stats['end_time'] - self.stats['start_time']).total_seconds()

            # 生成导入报告
            report = {
                'file_path': excel_file_path,
                'total_processed': self.stats['total_processed'],
                'total_success': self.stats['total_success'],
                'total_failed': self.stats['total_failed'],
                'duration_seconds': duration,
                'rows_per_second': self.stats['total_processed'] / duration if duration > 0 else 0,
                'success_rate': (self.stats['total_success'] / self.stats['total_processed'] * 100)
                              if self.stats['total_processed'] > 0 else 0
            }

            logger.info(f"导入完成！统计信息: {report}")
            return report

        except Exception as e:
            logger.error(f"导入失败: {e}")
            raise
        finally:
            self.db_manager.close_connection()

    def _process_excel_file(self, excel_file_path: str, sheet_name: str, skip_rows: int = 0) -> int:
        """处理Excel文件"""
        failed_records = []
        total_processed = 0

        try:
            # 读取整个Excel文件（Excel不支持chunksize）
            df = pd.read_excel(
                excel_file_path,
                sheet_name=sheet_name if sheet_name else 0,  # 默认读取第一个工作表
                skiprows=skip_rows if skip_rows > 0 else None
            )

            # 确保df是DataFrame
            if not isinstance(df, pd.DataFrame):
                logger.error(f"读取Excel文件失败，返回类型: {type(df)}")
                raise ValueError(f"Excel文件读取失败，期望DataFrame，实际得到: {type(df)}")

            # 手动分块处理
            total_rows = len(df)
            logger.info(f"Excel文件包含 {total_rows} 行数据")

            chunks = []
            for start in range(0, total_rows, self.config.chunk_size):
                end = min(start + self.config.chunk_size, total_rows)
                chunk_df = df.iloc[start:end]
                chunks.append(chunk_df)

            # 创建线程池
            with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
                futures = []

                for chunk_idx, chunk_df in enumerate(chunks):
                    if chunk_df.empty:
                        continue

                    # 数据预处理
                    processed_chunk = self._preprocess_chunk(chunk_df)

                    # 分批处理
                    batches = self._split_into_batches(processed_chunk, self.config.batch_size)

                    for batch_idx, batch in enumerate(batches):
                        future = executor.submit(self._process_batch, batch, chunk_idx, batch_idx)
                        futures.append(future)

                # 收集结果
                with tqdm(total=len(futures), desc="处理进度") as pbar:
                    for future in as_completed(futures):
                        try:
                            success_count, batch_failed = future.result()
                            self.stats['total_success'] += success_count
                            self.stats['total_failed'] += len(batch_failed)
                            failed_records.extend(batch_failed)

                        except Exception as e:
                            logger.error(f"批次处理失败: {e}")
                            self.stats['total_failed'] += self.config.batch_size

                        finally:
                            pbar.update(1)
                            total_processed += self.config.batch_size
                            self.stats['total_processed'] = total_processed

                            # 更新进度
                            self.progress_tracker.save_progress(
                                excel_file_path, total_processed, total_processed
                            )

            # 保存失败记录
            if failed_records:
                self._save_failed_records(failed_records)
                logger.warning(f"有 {len(failed_records)} 条记录导入失败，已保存到 {self.config.error_file}")

            return total_processed

        except Exception as e:
            logger.error(f"处理Excel文件失败: {e}")
            raise

    def _preprocess_chunk(self, chunk_df: pd.DataFrame) -> List[Dict]:
        """预处理数据块"""
        # 替换NaN值
        chunk_df = chunk_df.fillna('')

        # 转换为字典列表
        records = chunk_df.to_dict('records')

        # 字段映射和清理
        processed_records = []
        for record in records:
            processed_record = self._clean_record(record)
            if processed_record:
                processed_records.append(processed_record)

        return processed_records

    def _clean_record(self, record: Dict) -> Optional[Dict]:
        """清理单条记录"""
        cleaned_record = {}

        # 获取表字段名列表
        table_fields = [col['Field'] for col in self.table_columns]

        for key, value in record.items():
            # 跳过自增ID字段
            if key == 'id':
                continue

            # 只保留表中存在的字段
            if key in table_fields:
                # 数据类型转换
                cleaned_value = self._convert_data_type(key, value)
                cleaned_record[key] = cleaned_value

        return cleaned_record if cleaned_record else None

    def _convert_data_type(self, field_name: str, value: Any) -> Any:
        """转换数据类型"""
        if value is None or value == '':
            return None

        # 获取字段信息
        field_info = next((col for col in self.table_columns if col['Field'] == field_name), None)
        if not field_info:
            return value

        col_type = field_info['Type'].lower()

        try:
            if 'int' in col_type:
                return int(float(value)) if value != '' else None
            elif 'decimal' in col_type or 'float' in col_type:
                return float(value) if value != '' else None
            elif 'datetime' in col_type:
                if isinstance(value, str):
                    return pd.to_datetime(value).strftime('%Y-%m-%d %H:%M:%S')
                return value
            else:
                return str(value) if value is not None else None
        except:
            logger.warning(f"字段 {field_name} 数据类型转换失败: {value}")
            return None

    def _split_into_batches(self, records: List[Dict], batch_size: int) -> List[List[Dict]]:
        """将记录分割成批次"""
        batches = []
        for i in range(0, len(records), batch_size):
            batch = records[i:i + batch_size]
            batches.append(batch)
        return batches

    def _process_batch(self, batch: List[Dict], chunk_idx: int, batch_idx: int) -> Tuple[int, List[Dict]]:
        """处理单个批次"""
        thread_name = threading.current_thread().name
        logger.debug(f"[{thread_name}] 处理批次 {chunk_idx}-{batch_idx}, 记录数: {len(batch)}")

        # 数据验证
        if self.config.enable_validation and self.validator:
            validated_batch = []
            failed_records = []

            for record in batch:
                is_valid, errors = self.validator.validate_record(record)
                if is_valid:
                    validated_batch.append(record)
                else:
                    logger.warning(f"记录验证失败: {errors}")
                    record['_validation_errors'] = errors
                    failed_records.append(record)

            batch = validated_batch
        else:
            failed_records = []

        # 执行数据库插入
        if batch:
            success_count, db_failed = self.db_manager.execute_batch_insert(
                self.config.target_table, batch
            )
            failed_records.extend(db_failed)
            return success_count, failed_records
        else:
            return 0, failed_records

    def _save_failed_records(self, failed_records: List[Dict]):
        """保存失败的记录到Excel文件"""
        if not failed_records:
            return

        try:
            df_failed = pd.DataFrame(failed_records)
            df_failed.to_excel(self.config.error_file, index=False, engine='openpyxl')
            logger.info(f"失败记录已保存到: {self.config.error_file}")
        except Exception as e:
            logger.error(f"保存失败记录时出错: {e}")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='Excel到MySQL批量导入工具')
    parser.add_argument('excel_file', help='Excel文件路径')
    parser.add_argument('--sheet', help='工作表名称')
    parser.add_argument('--batch-size', type=int, default=1000, help='批次大小')
    parser.add_argument('--workers', type=int, default=4, help='线程数')
    parser.add_argument('--chunk-size', type=int, default=10000, help='读取块大小')
    parser.add_argument('--no-resume', action='store_true', help='禁用断点续传')
    parser.add_argument('--no-validation', action='store_true', help='禁用数据验证')

    args = parser.parse_args()

    # 创建配置
    config = ImportConfig(
        batch_size=args.batch_size,
        max_workers=args.workers,
        chunk_size=args.chunk_size,
        enable_resume=not args.no_resume,
        enable_validation=not args.no_validation
    )

    # 创建导入器
    importer = ExcelToMySQLImporter(config)

    try:
        # 初始化
        importer.initialize()

        # 执行导入
        result = importer.import_excel_file(args.excel_file, args.sheet)

        print("\n" + "="*60)
        print("导入完成！")
        print("="*60)
        print(f"文件: {result['file_path']}")
        print(f"总处理记录数: {result['total_processed']}")
        print(f"成功导入: {result['total_success']}")
        print(f"失败记录: {result['total_failed']}")
        print(f"成功率: {result['success_rate']:.2f}%")
        print(f"耗时: {result['duration_seconds']:.2f} 秒")
        print(f"处理速度: {result['rows_per_second']:.2f} 行/秒")
        print("="*60)

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
