#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的数据
"""

import pymysql

# 数据库连接配置
DB_CONFIG = {
    'host': '************',
    'user': 'lxt',
    'password': 'Lxt0307+',
    'database': 'qq_day_sale',
    'charset': 'utf8mb4',
    'autocommit': False
}

def check_database():
    """检查数据库中的数据"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 查询记录数
        cursor.execute("SELECT COUNT(*) FROM wdt_saledetail")
        count = cursor.fetchone()[0]
        print(f"表中当前有 {count} 条记录")
        
        if count > 0:
            # 查询最新的几条记录
            cursor.execute("SELECT 订单编号, 货品名称, 货品数量, 货品成交价, 客户网名 FROM wdt_saledetail ORDER BY id DESC LIMIT 5")
            records = cursor.fetchall()
            
            print("\n最新的记录:")
            print("-" * 80)
            for record in records:
                print(f"订单编号: {record[0]}, 货品名称: {record[1]}, 数量: {record[2]}, 成交价: {record[3]}, 客户: {record[4]}")
        
        connection.close()
        
    except Exception as e:
        print(f"检查数据库失败: {e}")

if __name__ == "__main__":
    check_database()
