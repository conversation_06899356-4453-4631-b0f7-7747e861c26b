#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试导入器
"""

from excel_to_mysql_importer import ExcelToMySQLImporter, ImportConfig

def direct_test():
    """直接测试导入器"""
    try:
        # 创建配置，禁用断点续传
        config = ImportConfig(
            enable_resume=False,
            enable_validation=False,  # 暂时禁用验证
            batch_size=10,
            max_workers=1
        )
        
        # 创建导入器
        importer = ExcelToMySQLImporter(config)
        
        # 初始化
        print("初始化导入器...")
        importer.initialize()
        
        # 执行导入
        print("开始导入...")
        result = importer.import_excel_file('sample_data.xlsx')
        
        print("导入结果:")
        print(result)
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    direct_test()
