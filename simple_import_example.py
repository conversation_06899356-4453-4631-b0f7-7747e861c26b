#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Excel导入示例
适合快速上手使用
"""

import os
import sys
from excel_to_mysql_importer import ExcelToMySQLImporter, ImportConfig

def simple_import(excel_file_path: str, sheet_name: str = None):
    """
    简单的Excel导入函数
    
    Args:
        excel_file_path: Excel文件路径
        sheet_name: 工作表名称，None表示第一个工作表
    """
    
    # 检查文件是否存在
    if not os.path.exists(excel_file_path):
        print(f"错误: Excel文件不存在 - {excel_file_path}")
        return False
    
    print(f"开始导入Excel文件: {excel_file_path}")
    print("=" * 60)
    
    try:
        # 创建导入配置
        config = ImportConfig(
            # 数据库配置
            db_host='************',
            db_user='lxt',
            db_password='Lxt0307+',
            db_database='qq_day_sale',
            target_table='wdt_saledetail',
            
            # 性能配置
            batch_size=1000,    # 每批次1000条记录
            max_workers=4,      # 4个线程
            chunk_size=10000,   # 每次读取10000行
            
            # 功能配置
            enable_resume=True,      # 启用断点续传
            enable_validation=True,  # 启用数据验证
            skip_duplicates=True     # 跳过重复数据
        )
        
        # 创建导入器
        importer = ExcelToMySQLImporter(config)
        
        # 初始化
        print("正在初始化导入器...")
        importer.initialize()
        
        # 执行导入
        print("开始导入数据...")
        result = importer.import_excel_file(excel_file_path, sheet_name)
        
        # 显示结果
        print("\n" + "=" * 60)
        print("导入完成！")
        print("=" * 60)
        print(f"文件路径: {result['file_path']}")
        print(f"总处理记录数: {result['total_processed']:,}")
        print(f"成功导入: {result['total_success']:,}")
        print(f"失败记录: {result['total_failed']:,}")
        print(f"成功率: {result['success_rate']:.2f}%")
        print(f"耗时: {result['duration_seconds']:.2f} 秒")
        print(f"处理速度: {result['rows_per_second']:.2f} 行/秒")
        
        if result['total_failed'] > 0:
            print(f"\n注意: 有 {result['total_failed']} 条记录导入失败")
            print("失败记录已保存到 import_errors.xlsx 文件中")
        
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"导入失败: {e}")
        return False

def batch_import(excel_files: list, sheet_name: str = None):
    """
    批量导入多个Excel文件
    
    Args:
        excel_files: Excel文件路径列表
        sheet_name: 工作表名称
    """
    print(f"开始批量导入 {len(excel_files)} 个Excel文件")
    print("=" * 60)
    
    success_count = 0
    failed_files = []
    
    for i, excel_file in enumerate(excel_files, 1):
        print(f"\n[{i}/{len(excel_files)}] 正在处理: {os.path.basename(excel_file)}")
        
        if simple_import(excel_file, sheet_name):
            success_count += 1
            print(f"✓ 文件 {os.path.basename(excel_file)} 导入成功")
        else:
            failed_files.append(excel_file)
            print(f"✗ 文件 {os.path.basename(excel_file)} 导入失败")
    
    # 批量导入总结
    print("\n" + "=" * 60)
    print("批量导入完成！")
    print("=" * 60)
    print(f"总文件数: {len(excel_files)}")
    print(f"成功导入: {success_count}")
    print(f"失败文件: {len(failed_files)}")
    
    if failed_files:
        print("\n失败的文件:")
        for file in failed_files:
            print(f"  - {file}")

def main():
    """主函数 - 命令行使用"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python simple_import_example.py <excel_file> [sheet_name]")
        print("  python simple_import_example.py file1.xlsx file2.xlsx file3.xlsx")
        print("\n示例:")
        print("  python simple_import_example.py sales_data.xlsx")
        print("  python simple_import_example.py sales_data.xlsx Sheet1")
        print("  python simple_import_example.py file1.xlsx file2.xlsx file3.xlsx")
        sys.exit(1)
    
    excel_files = sys.argv[1:]
    sheet_name = None
    
    # 如果只有一个文件且提供了工作表名称
    if len(excel_files) == 2 and not excel_files[1].endswith('.xlsx'):
        excel_file = excel_files[0]
        sheet_name = excel_files[1]
        simple_import(excel_file, sheet_name)
    
    # 如果是单个文件
    elif len(excel_files) == 1:
        simple_import(excel_files[0])
    
    # 如果是多个文件
    else:
        batch_import(excel_files)

if __name__ == "__main__":
    main()
