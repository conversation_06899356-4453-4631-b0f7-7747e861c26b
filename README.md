# Excel到MySQL高性能批量导入工具

这是一个专为`wdt_saledetail`表设计的高性能Excel批量导入工具，支持多线程并发、大文件处理、断点续传等功能。

## 功能特性

- ✅ **多线程并发导入** - 支持多线程并行处理，大幅提升导入速度
- ✅ **大文件处理** - 分块读取Excel文件，支持处理GB级别的大文件
- ✅ **断点续传** - 支持中断后继续导入，避免重复处理
- ✅ **数据验证** - 自动验证数据类型和必填字段
- ✅ **错误处理** - 详细的错误记录和失败数据导出
- ✅ **进度显示** - 实时显示导入进度和性能统计
- ✅ **日志记录** - 完整的操作日志记录
- ✅ **配置灵活** - 支持多种配置选项和性能调优

## 环境要求

### Python版本
- Python 3.7+

### 依赖包
```bash
pip install pandas pymysql openpyxl tqdm numpy
```

### 数据库要求
- MySQL 5.7+
- 目标表：`wdt_saledetail` (94个字段)

## 快速开始

### 1. 基本使用

```python
from excel_to_mysql_importer import ExcelToMySQLImporter, ImportConfig

# 创建配置
config = ImportConfig()

# 创建导入器
importer = ExcelToMySQLImporter(config)

# 初始化
importer.initialize()

# 导入Excel文件
result = importer.import_excel_file('sales_data.xlsx')
print(f"导入完成，成功: {result['total_success']}, 失败: {result['total_failed']}")
```

### 2. 命令行使用

```bash
# 导入单个文件
python excel_to_mysql_importer.py sales_data.xlsx

# 指定工作表
python excel_to_mysql_importer.py sales_data.xlsx --sheet Sheet1

# 自定义参数
python excel_to_mysql_importer.py sales_data.xlsx --batch-size 2000 --workers 8

# 禁用断点续传
python excel_to_mysql_importer.py sales_data.xlsx --no-resume
```

### 3. 简化使用

```python
from simple_import_example import simple_import

# 一行代码导入
simple_import('sales_data.xlsx')

# 批量导入多个文件
from simple_import_example import batch_import
batch_import(['file1.xlsx', 'file2.xlsx', 'file3.xlsx'])
```

## 配置说明

### 数据库连接配置

```python
config = ImportConfig(
    db_host='************',      # 数据库主机
    db_user='lxt',               # 用户名
    db_password='Lxt0307+',      # 密码
    db_database='qq_day_sale',   # 数据库名
    target_table='wdt_saledetail' # 目标表
)
```

### 性能配置

```python
config = ImportConfig(
    batch_size=1000,    # 每批次插入记录数 (建议: 500-2000)
    max_workers=4,      # 线程数 (建议: CPU核心数)
    chunk_size=10000    # Excel读取块大小 (建议: 5000-50000)
)
```

### 功能配置

```python
config = ImportConfig(
    enable_resume=True,      # 启用断点续传
    enable_validation=True,  # 启用数据验证
    skip_duplicates=True     # 跳过重复数据
)
```

## 性能优化建议

### 根据文件大小调整参数

| 文件大小 | batch_size | max_workers | chunk_size |
|---------|------------|-------------|------------|
| < 10MB  | 500        | 2-4         | 5,000      |
| 10-100MB| 1000       | 4-6         | 10,000     |
| > 100MB | 2000       | 6-8         | 20,000     |

### 根据系统资源调整

| 系统配置 | 推荐设置 |
|---------|---------|
| 2核4GB  | workers=2, chunk_size=5000  |
| 4核8GB  | workers=4, chunk_size=10000 |
| 8核16GB | workers=6, chunk_size=20000 |
| 16核32GB| workers=8, chunk_size=50000 |

## Excel文件格式要求

### 支持的格式
- `.xlsx` (推荐)
- `.xls`

### 字段映射
Excel列名需要与数据库字段名对应，支持的字段包括：

**订单信息**
- 订单编号、原始单号、子单原始单号、原始子订单号
- 订单类型、支付账号

**出库信息**  
- 出库单编号、仓库、仓库类型、店铺
- 出库单状态、出库状态、分拣序号

**商品信息**
- 商家编码、货品编号、货品名称、货品简称
- 品牌、分类、规格码、规格名称
- 平台货品名称、平台规格名称、条形码

**金额信息**
- 货品数量、货品原单价、货品原总金额
- 订单总优惠、邮费、货品成交价、货品成交总价
- 各种成本和毛利字段

**客户信息**
- 客户网名、收件人、证件号码
- 收货地区、收货地址、收件人手机、收件人电话

**时间信息**
- 付款时间、发货时间、下单时间、审核时间

完整字段列表请参考 `wdt_saledetail_columns.csv` 文件。

## 错误处理

### 常见错误及解决方案

1. **连接数据库失败**
   - 检查数据库连接参数
   - 确认网络连接正常
   - 验证用户权限

2. **Excel文件读取失败**
   - 确认文件格式正确
   - 检查文件是否被占用
   - 验证文件路径正确

3. **数据验证失败**
   - 检查必填字段是否为空
   - 验证数据类型是否匹配
   - 查看错误日志获取详细信息

4. **内存不足**
   - 减小 `chunk_size` 参数
   - 减少 `max_workers` 数量
   - 分批处理大文件

### 日志文件

- `excel_import.log` - 详细操作日志
- `import_errors.xlsx` - 失败记录导出
- `import_progress.json` - 断点续传进度

## 高级功能

### 断点续传

工具会自动记录导入进度，如果导入过程中断，重新运行时会从上次中断的位置继续：

```python
# 启用断点续传（默认启用）
config = ImportConfig(enable_resume=True)
```

### 数据验证

自动验证数据类型和必填字段：

```python
# 启用数据验证（默认启用）
config = ImportConfig(enable_validation=True)
```

### 性能监控

实时显示导入进度和性能统计：

```
处理进度: 100%|██████████| 50/50 [02:30<00:00,  3.00it/s]

导入完成！
============================================================
文件: sales_data.xlsx
总处理记录数: 50,000
成功导入: 49,850
失败记录: 150
成功率: 99.70%
耗时: 150.25 秒
处理速度: 332.50 行/秒
============================================================
```

## 故障排除

### 性能问题
1. 调整批次大小和线程数
2. 检查数据库连接池设置
3. 优化数据库索引

### 内存问题
1. 减小chunk_size参数
2. 减少并发线程数
3. 分批处理大文件

### 数据问题
1. 检查Excel格式和编码
2. 验证字段映射关系
3. 查看详细错误日志

## 技术支持

如遇到问题，请检查：
1. 日志文件 `excel_import.log`
2. 错误数据文件 `import_errors.xlsx`
3. 进度文件 `import_progress.json`

## 版本信息

- 版本: 1.0
- 作者: AI Assistant
- 日期: 2025-08-04
- Python版本: 3.7+
