#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试导入问题
"""

import pandas as pd

def debug_excel_reading():
    """调试Excel读取"""
    try:
        # 读取Excel文件
        df = pd.read_excel('sample_data.xlsx')
        
        print(f"DataFrame类型: {type(df)}")
        print(f"DataFrame形状: {df.shape}")
        print(f"是否为空: {df.empty}")
        
        # 测试iloc
        if len(df) > 0:
            chunk_df = df.iloc[0:5]
            print(f"切片结果类型: {type(chunk_df)}")
            print(f"切片结果形状: {chunk_df.shape}")
        
        # 测试fillna
        df_filled = df.fillna('')
        print(f"fillna后类型: {type(df_filled)}")
        
        # 测试to_dict
        records = df_filled.to_dict('records')
        print(f"records类型: {type(records)}")
        print(f"records长度: {len(records)}")
        if records:
            print(f"第一条记录类型: {type(records[0])}")
            print(f"第一条记录: {records[0]}")
        
        return df
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    debug_excel_reading()
