#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库连接和表结构
"""

import pymysql
import pandas as pd
from datetime import datetime

# 数据库连接配置
DB_CONFIG = {
    'host': '************',
    'user': 'lxt',
    'password': 'Lxt0307+',
    'database': 'qq_day_sale',
    'charset': 'utf8mb4',
    'autocommit': False
}

def test_database_connection():
    """测试数据库连接"""
    try:
        print("正在测试数据库连接...")
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 测试连接
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()[0]
        print(f"✓ 数据库连接成功！MySQL版本: {version}")
        
        # 检查目标表是否存在
        cursor.execute("SHOW TABLES LIKE 'wdt_saledetail'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✓ 目标表 wdt_saledetail 存在")
            
            # 获取表行数
            cursor.execute("SELECT COUNT(*) FROM wdt_saledetail")
            row_count = cursor.fetchone()[0]
            print(f"✓ 表中当前有 {row_count} 条记录")
            
        else:
            print("✗ 目标表 wdt_saledetail 不存在")
            return False
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        return False

def create_sample_excel():
    """创建示例Excel文件用于测试"""
    print("\n正在创建示例Excel文件...")
    
    # 创建示例数据
    sample_data = []
    
    for i in range(1, 11):  # 创建10条示例记录
        record = {
            '订单编号': f'ORDER{i:06d}',
            '原始单号': f'ORIG{i:06d}',
            '出库单编号': f'OUT{i:06d}',
            '仓库': '主仓库',
            '店铺': '测试店铺',
            '商家编码': f'GOODS{i:03d}',
            '货品编号': f'ITEM{i:03d}',
            '货品名称': f'测试商品{i}',
            '品牌': '测试品牌',
            '分类': '测试分类',
            '规格名称': f'规格{i}',
            '货品数量': i * 2,
            '货品原单价': round(99.99 + i, 2),
            '货品原总金额': round((99.99 + i) * (i * 2), 2),
            '货品成交价': round(89.99 + i, 2),
            '货品成交总价': round((89.99 + i) * (i * 2), 2),
            '客户网名': f'客户{i}',
            '收件人': f'收件人{i}',
            '收货地区': '北京市朝阳区',
            '收货地址': f'测试地址{i}号',
            '收件人手机': f'138{i:08d}',
            '物流公司': '顺丰快递',
            '付款时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            '下单时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            '备注': f'测试备注{i}'
        }
        sample_data.append(record)
    
    # 创建DataFrame并保存为Excel
    df = pd.DataFrame(sample_data)
    excel_file = 'sample_data.xlsx'
    df.to_excel(excel_file, index=False, engine='openpyxl')
    
    print(f"✓ 示例Excel文件已创建: {excel_file}")
    print(f"✓ 包含 {len(sample_data)} 条示例记录")
    
    return excel_file

def main():
    """主函数"""
    print("=" * 60)
    print("Excel到MySQL导入工具 - 连接测试")
    print("=" * 60)
    
    # 测试数据库连接
    if test_database_connection():
        print("\n数据库连接测试通过！")
        
        # 创建示例Excel文件
        excel_file = create_sample_excel()
        
        print(f"\n准备工作完成！")
        print(f"现在可以使用以下命令测试导入功能:")
        print(f"python simple_import_example.py {excel_file}")
        
    else:
        print("\n数据库连接测试失败，请检查连接配置！")

if __name__ == "__main__":
    main()
