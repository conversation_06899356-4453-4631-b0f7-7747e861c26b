#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel到MySQL导入工具配置文件
"""

from dataclasses import dataclass
from typing import Dict, List, Optional

@dataclass
class DatabaseConfig:
    """数据库连接配置"""
    host: str = '************'
    user: str = 'lxt'
    password: str = 'Lxt0307+'
    database: str = 'qq_day_sale'
    charset: str = 'utf8mb4'
    port: int = 3306

@dataclass
class ImportSettings:
    """导入设置"""
    target_table: str = 'wdt_saledetail'
    batch_size: int = 1000          # 每批次插入记录数
    max_workers: int = 4            # 最大线程数
    chunk_size: int = 10000         # Excel读取块大小
    
    # 功能开关
    enable_resume: bool = True      # 启用断点续传
    enable_validation: bool = True  # 启用数据验证
    skip_duplicates: bool = True    # 跳过重复数据
    
    # 文件路径
    progress_file: str = 'import_progress.json'
    error_file: str = 'import_errors.xlsx'
    log_file: str = 'excel_import.log'

@dataclass
class FieldMapping:
    """字段映射配置"""
    # Excel列名到数据库字段名的映射
    # 格式: {'Excel列名': '数据库字段名'}
    column_mapping: Dict[str, str] = None
    
    # 需要跳过的Excel列
    skip_columns: List[str] = None
    
    # 默认值设置
    default_values: Dict[str, any] = None
    
    def __post_init__(self):
        if self.column_mapping is None:
            self.column_mapping = {}
        if self.skip_columns is None:
            self.skip_columns = []
        if self.default_values is None:
            self.default_values = {}

# 预定义的字段映射示例
COMMON_FIELD_MAPPINGS = {
    # 订单相关
    'order_no': '订单编号',
    'original_order_no': '原始单号',
    'sub_original_order_no': '子单原始单号',
    'original_sub_order_no': '原始子订单号',
    'order_type': '订单类型',
    'payment_account': '支付账号',
    
    # 出库相关
    'outbound_no': '出库单编号',
    'warehouse': '仓库',
    'warehouse_type': '仓库类型',
    'store': '店铺',
    'outbound_status': '出库单状态',
    'delivery_status': '出库状态',
    'sorting_no': '分拣序号',
    
    # 商品相关
    'merchant_code': '商家编码',
    'goods_no': '货品编号',
    'goods_name': '货品名称',
    'goods_short_name': '货品简称',
    'brand': '品牌',
    'category': '分类',
    'spec_code': '规格码',
    'spec_name': '规格名称',
    'platform_goods_name': '平台货品名称',
    'platform_spec_name': '平台规格名称',
    'platform_goods_id': '平台货品ID',
    'platform_spec_id': '平台规格ID',
    'barcode': '条形码',
    
    # 数量和金额
    'goods_quantity': '货品数量',
    'goods_original_price': '货品原单价',
    'goods_original_total': '货品原总金额',
    'order_total_discount': '订单总优惠',
    'postage': '邮费',
    'goods_deal_price': '货品成交价',
    'goods_deal_total': '货品成交总价',
    'goods_total_discount': '货品总优惠',
    'cod_amount': '货到付款金额',
    'goods_cost': '货品成本',
    'goods_total_cost': '货品总成本',
    'fixed_cost': '固定成本',
    'fixed_total_cost': '固定总成本',
    'order_payment_amount': '订单支付金额',
    'receivable_amount': '应收金额',
    'pre_refund_payment': '退款前支付金额',
    'single_payment_amount': '单品支付金额',
    'shared_postage': '分摊邮费',
    'estimated_postage': '预估邮资',
    'postage_cost': '邮资成本',
    'order_packaging_cost': '订单包装成本',
    'order_gross_profit': '订单毛利',
    'gross_profit_rate': '毛利率',
    'order_fixed_gross_profit': '订单固定毛利',
    'fixed_gross_profit_rate': '固定毛利率',
    
    # 客户信息
    'customer_name': '客户网名',
    'recipient': '收件人',
    'id_number': '证件号码',
    'delivery_area': '收货地区',
    'delivery_address': '收货地址',
    'recipient_mobile': '收件人手机',
    'recipient_phone': '收件人电话',
    
    # 物流信息
    'logistics_company': '物流公司',
    'actual_weight': '实际重量',
    'estimated_weight': '预估重量',
    'need_invoice': '需开发票',
    
    # 操作人员
    'order_creator': '制单人',
    'printer': '打单员',
    'picker': '拣货员',
    'packer': '打包员',
    'inspector': '检视员',
    'salesperson': '业务员',
    'checker': '验货员',
    
    # 打印状态
    'print_batch': '打印波次',
    'logistics_print_status': '物流单打印状态',
    'delivery_print_status': '发货单打印状态',
    'sorting_print_status': '分拣单打印状态',
    
    # 单号信息
    'logistics_no': '物流单号',
    'sorting_no': '分拣单编号',
    'external_no': '外部单号',
    
    # 时间信息
    'payment_time': '付款时间',
    'delivery_time': '发货时间',
    'order_time': '下单时间',
    'audit_time': '审核时间',
    
    # 备注信息
    'gift_method': '赠品方式',
    'buyer_message': '买家留言',
    'service_note': '客服备注',
    'print_note': '打印备注',
    'note': '备注',
    'packaging': '包装',
    
    # 组合装信息
    'source_combo_code': '来源组合装编码',
    'from_combo': '拆自组合装',
    'source_combo_quantity': '来源组合装数量',
    'volume': '体积',
    
    # 分销信息
    'distributor': '分销商',
    'distributor_code': '分销商编号',
    'distributor_original_no': '分销原始单号'
}

# 数据类型映射
DATA_TYPE_MAPPING = {
    'text': str,
    'varchar': str,
    'char': str,
    'int': int,
    'bigint': int,
    'decimal': float,
    'float': float,
    'double': float,
    'datetime': 'datetime',
    'date': 'date',
    'time': 'time',
    'timestamp': 'datetime'
}

# 默认配置实例
DEFAULT_DB_CONFIG = DatabaseConfig()
DEFAULT_IMPORT_SETTINGS = ImportSettings()
DEFAULT_FIELD_MAPPING = FieldMapping()

# 性能优化建议
PERFORMANCE_TIPS = {
    'batch_size': {
        'small_file': 500,      # < 10MB
        'medium_file': 1000,    # 10MB - 100MB  
        'large_file': 2000      # > 100MB
    },
    'max_workers': {
        'cpu_cores_2': 2,
        'cpu_cores_4': 4,
        'cpu_cores_8': 6,
        'cpu_cores_16': 8
    },
    'chunk_size': {
        'memory_4gb': 5000,
        'memory_8gb': 10000,
        'memory_16gb': 20000,
        'memory_32gb': 50000
    }
}

def get_optimal_config(file_size_mb: int, cpu_cores: int, memory_gb: int) -> ImportSettings:
    """
    根据系统资源和文件大小获取最优配置
    
    Args:
        file_size_mb: 文件大小(MB)
        cpu_cores: CPU核心数
        memory_gb: 内存大小(GB)
        
    Returns:
        优化后的导入设置
    """
    settings = ImportSettings()
    
    # 根据文件大小调整批次大小
    if file_size_mb < 10:
        settings.batch_size = PERFORMANCE_TIPS['batch_size']['small_file']
    elif file_size_mb < 100:
        settings.batch_size = PERFORMANCE_TIPS['batch_size']['medium_file']
    else:
        settings.batch_size = PERFORMANCE_TIPS['batch_size']['large_file']
    
    # 根据CPU核心数调整线程数
    if cpu_cores <= 2:
        settings.max_workers = 2
    elif cpu_cores <= 4:
        settings.max_workers = 4
    elif cpu_cores <= 8:
        settings.max_workers = 6
    else:
        settings.max_workers = 8
    
    # 根据内存大小调整块大小
    if memory_gb <= 4:
        settings.chunk_size = 5000
    elif memory_gb <= 8:
        settings.chunk_size = 10000
    elif memory_gb <= 16:
        settings.chunk_size = 20000
    else:
        settings.chunk_size = 50000
    
    return settings
